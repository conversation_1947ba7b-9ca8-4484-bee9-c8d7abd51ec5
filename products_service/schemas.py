"""
Schemas for product model, for creating, updating and the response.
"""
import datetime
from typing import Optional, List
from pydantic import BaseModel, ConfigDict
from decimal import Decimal


class CategoryBase(BaseModel):
    name: str
    description: Optional[str] = None
    is_active: Optional[bool] = True


class CategoryCreate(CategoryBase):
    pass


class CategoryUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None


class CategoryResponse(CategoryBase):
    id: int
    created_at: Optional[datetime.datetime] = None
    updated_at: Optional[datetime.datetime] = None

    model_config = ConfigDict(from_attributes=True)

class ProductBase(BaseModel):
    name: str
    description: Optional[str] = None
    price: Decimal
    currency: Optional[str] = "USD"
    in_stock: Optional[bool] = True
    stock_quantity: Optional[int] = 0
    unity_of_measure: Optional[str] = "lb"
    categories: Optional[List[str]] = []
    tags: Optional[List[str]] = []
    images: Optional[List[str]] = []

class ProductCreate(ProductBase):
    pass

class ProductUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    price: Optional[Decimal] = None
    currency: Optional[str] = None
    in_stock: Optional[bool] = None
    stock_quantity: Optional[int] = None
    unity_of_measure: Optional[str] = None
    categories: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    images: Optional[List[str]] = None


class ProductResponse(ProductBase):
    id: int
    categories: List[CategoryResponse] = []
    created_at: Optional[datetime.datetime] = None
    updated_at: Optional[datetime.datetime] = None

    model_config = ConfigDict(from_attributes=True)
