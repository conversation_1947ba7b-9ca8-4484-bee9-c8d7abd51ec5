"""
Simple JWT authentication for products service.
Verifies tokens using external auth service.
"""

import httpx
import time
from typing import Optional, Dict, Any
from fastapi import HTTPException, status, Depends, Header

# Cache for user data
_cache: Dict[str, Any] = {}

# External auth service URLs
AUTH_USER_ME_URL = "https://fehdan-auth-service.onrender.com/api/v1/users/me"


async def get_user_from_token(token: str) -> Dict[str, Any]:
    """
    Get user data using JWT token from /api/v1/users/me endpoint.

    Args:
        token: JWT token to use for authentication

    Returns:
        Dict: User data from the API

    Raises:
        HTTPException: If token verification fails
    """
    # Check cache first
    cache_key = f"token_{hash(token)}"
    current_time = time.time()

    if cache_key in _cache:
        cached_data = _cache[cache_key]
        if current_time - cached_data["timestamp"] < 60:  # 1 minute cache
            return cached_data["user"]

    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(
                AUTH_USER_ME_URL,
                headers={"Authorization": f"Bearer {token}"}
            )

            if response.status_code == 401:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid or expired token",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            response.raise_for_status()
            user_data = response.json()

            # Cache the result
            _cache[cache_key] = {
                "user": user_data,
                "timestamp": current_time
            }

            return user_data

    except httpx.HTTPError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token verification failed",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication service error",
        )


async def get_current_user(
    authorization: Optional[str] = Header(None, description="Bearer token")
) -> Dict[str, Any]:
    """
    FastAPI dependency to get current authenticated user.

    Args:
        authorization: Authorization header containing Bearer token

    Returns:
        Dict: User data from the API

    Raises:
        HTTPException: If authentication fails
    """
    if not authorization:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authorization header is required",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Extract token from Authorization header
    try:
        scheme, token = authorization.split(" ", 1)
        if scheme.lower() != "bearer":
            raise ValueError("Invalid scheme")
        token = token.strip()
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authorization header format. Expected 'Bearer <token>'",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return await get_user_from_token(token)


async def require_write_access(user: Dict[str, Any] = Depends(get_current_user)) -> Dict[str, Any]:
    """
    Dependency that requires write access (admin role only for now).

    Args:
        user: Current authenticated user data

    Returns:
        Dict: Authenticated user data with write access

    Raises:
        HTTPException: If user doesn't have write access
    """
    # For now, only allow admin role to write
    # You can adjust this logic based on your requirements
    if user.get("role") != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required for write operations.",
        )
    return user
