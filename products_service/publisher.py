"""
Event publisher for products service.
Utility functions to publish product-related events.
"""

import time
from shared.events import Event, PRODUCT_CREATED, PRODUCT_UPDATED, PRODUCT_DELETED
from shared.redis_client import publish_event
from shared.core.utils.logging import LoggingService

logger = LoggingService()


async def publish_product_created_event(
    product_data: dict, user_id: int = 1, location_id: int = 1
):
    """Publish PRODUCT_CREATED event."""
    try:
        event_data = {
            "product_id": product_data["id"],
            "product_name": product_data["name"],
            "stock_quantity": product_data.get("stock_quantity", 0),
            "price": float(product_data["price"]),
            "currency": product_data.get("currency", "USD"),
            "user_id": user_id,
            "location_id": location_id,
            "reorder_point": 10,  # Default reorder point
            "created_by": f"user_{user_id}",
        }

        event = Event(type=PRODUCT_CREATED, data=event_data, timestamp=time.time())

        await publish_event(event, channel="product_events")
        logger.log(
            f"Published PRODUCT_CREATED event for product {product_data['id']}",
            level="info",
            app_name="products",
        )
        return True
    except Exception as e:
        logger.log(
            f"Failed to publish PRODUCT_CREATED event: {str(e)}",
            level="error",
            exception=e,
            app_name="products",
        )
        return False


async def publish_product_updated_event(product_data: dict, updated_fields: list):
    """Publish PRODUCT_UPDATED event."""
    try:
        event_data = {
            "product_id": product_data["id"],
            "product_name": product_data["name"],
            "stock_quantity": product_data.get("stock_quantity", 0),
            "price": float(product_data["price"]),
            "currency": product_data.get("currency", "USD"),
            "updated_fields": updated_fields,
        }

        event = Event(type=PRODUCT_UPDATED, data=event_data, timestamp=time.time())

        await publish_event(event, channel="product_events")
        logger.log(
            f"Published PRODUCT_UPDATED event for product {product_data['id']}",
            level="info",
            app_name="products",
        )
        return True
    except Exception as e:
        logger.log(
            f"Failed to publish PRODUCT_UPDATED event: {str(e)}",
            level="error",
            exception=e,
            app_name="products",
        )
        return False


async def publish_product_deleted_event(product_id: int, product_name: str):
    """Publish PRODUCT_DELETED event."""
    try:
        event_data = {
            "product_id": product_id,
            "product_name": product_name,
        }

        event = Event(type=PRODUCT_DELETED, data=event_data, timestamp=time.time())

        await publish_event(event, channel="product_events")
        logger.log(
            f"Published PRODUCT_DELETED event for product {product_id}",
            level="info",
            app_name="products",
        )
        return True
    except Exception as e:
        logger.log(
            f"Failed to publish PRODUCT_DELETED event: {str(e)}",
            level="error",
            exception=e,
            app_name="products",
        )
        return False
