"""
Kafka configuration for Products Service.

This module provides Kafka connection and configuration following
the fehdan-inventory service patterns.
"""

import os
from typing import Dict, Any
from confluent_kafka import Producer, Consumer
from app.core.config import settings


def get_kafka_config() -> Dict[str, Any]:
    """Get Kafka configuration from environment variables."""
    config = {
        'bootstrap.servers': os.getenv('KAFKA_BOOTSTRAP_SERVERS', 'localhost:9092'),
        'security.protocol': os.getenv('KAFKA_SECURITY_PROTOCOL', 'PLAINTEXT'),
    }
    
    # Add SASL configuration if needed
    if config['security.protocol'] in ['SASL_SSL', 'SASL_PLAINTEXT']:
        config.update({
            'sasl.mechanism': os.getenv('KAFKA_SASL_MECHANISM', 'PLAIN'),
            'sasl.username': os.getenv('KAFKA_SASL_USERNAME', ''),
            'sasl.password': os.getenv('KAFKA_SASL_PASSWORD', ''),
        })
    
    # Add SSL configuration if needed
    if config['security.protocol'] in ['SSL', 'SASL_SSL']:
        config.update({
            'ssl.ca.location': os.getenv('KAFKA_SSL_CA_LOCATION', ''),
            'ssl.certificate.location': os.getenv('KAFKA_SSL_CERT_LOCATION', ''),
            'ssl.key.location': os.getenv('KAFKA_SSL_KEY_LOCATION', ''),
        })
    
    return config


def get_producer_config() -> Dict[str, Any]:
    """Get Kafka producer configuration."""
    config = get_kafka_config()
    config.update({
        'client.id': f'products-service-producer-{os.getpid()}',
        'acks': 'all',  # Wait for all replicas to acknowledge
        'retries': 3,
        'retry.backoff.ms': 1000,
        'delivery.timeout.ms': 30000,
        'request.timeout.ms': 25000,
    })
    return config


def get_consumer_config(group_id: str = None) -> Dict[str, Any]:
    """Get Kafka consumer configuration."""
    config = get_kafka_config()
    config.update({
        'group.id': group_id or f'products-service-{os.getpid()}',
        'client.id': f'products-service-consumer-{os.getpid()}',
        'auto.offset.reset': 'earliest',
        'enable.auto.commit': True,
        'auto.commit.interval.ms': 5000,
        'session.timeout.ms': 30000,
        'heartbeat.interval.ms': 10000,
    })
    return config


def create_producer() -> Producer:
    """Create and return a Kafka producer instance."""
    config = get_producer_config()
    return Producer(config)


def create_consumer(group_id: str = None, topics: list = None) -> Consumer:
    """Create and return a Kafka consumer instance."""
    config = get_consumer_config(group_id)
    consumer = Consumer(config)
    
    if topics:
        consumer.subscribe(topics)
    
    return consumer


# Topic names based on service configuration
TOPICS = {
    'products': settings.PRODUCTS_CHANNEL,
    'inventory': settings.INVENTORY_CHANNEL,
    'orders': settings.ORDERS_CHANNEL,
    'auth': settings.AUTH_CHANNEL,
}
