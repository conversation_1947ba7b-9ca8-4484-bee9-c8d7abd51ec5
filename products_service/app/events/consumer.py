"""
Event consumer for Products Service.

This module provides event consumption capabilities following the
fehdan-inventory service patterns.
"""

import json
import asyncio
import logging
from typing import Dict, Any, Callable
from confluent_kafka import Consumer, KafkaError
from app.events.kafka_config import create_consumer, TOPICS
from app.events.schemas import (
    InventoryEventType,
    OrderEventType,
    InventoryItemCreatedEvent,
    OrderCreatedEvent,
    ALL_EVENT_MAPPINGS,
)
from app.core.logging import LoggingService

logger = logging.getLogger(__name__)


class EventConsumer:
    """
    Event consumer for Products Service.
    
    Handles consuming events from Kafka topics and processing them.
    """
    
    def __init__(self):
        self.consumer = None
        self.logger = LoggingService()
        self.event_handlers: Dict[str, Callable] = {}
        self.running = False
        
        # Register event handlers
        self._register_handlers()
    
    def _register_handlers(self):
        """Register event handlers for different event types."""
        self.event_handlers = {
            InventoryEventType.INVENTORY_ITEM_CREATED: self.handle_inventory_item_created,
            OrderEventType.ORDER_CREATED: self.handle_order_created,
        }
    
    async def handle_inventory_item_created(self, event_data: dict):
        """Handle INVENTORY_ITEM_CREATED event."""
        try:
            event = InventoryItemCreatedEvent(**event_data)
            self.logger.log(
                f"Processing inventory item created event: {event.event_id}",
                level="info",
                app_name="products"
            )
            
            # Business logic for handling inventory item creation
            inventory_data = event.data
            product_id = inventory_data.get("product_id")
            item_id = inventory_data.get("item_id")
            
            self.logger.log(
                f"Inventory item {item_id} created for product {product_id}",
                level="info",
                app_name="products"
            )
            
            # TODO: Implement any business logic needed when inventory items are created
            
        except Exception as e:
            self.logger.log(
                f"Error handling INVENTORY_ITEM_CREATED event: {e}",
                level="error",
                exception=e,
                app_name="products"
            )
    
    async def handle_order_created(self, event_data: dict):
        """Handle ORDER_CREATED event."""
        try:
            event = OrderCreatedEvent(**event_data)
            self.logger.log(
                f"Processing order created event: {event.event_id}",
                level="info",
                app_name="products"
            )
            
            # Business logic for handling order creation
            order_data = event.data
            order_id = order_data.get("order_id")
            items = order_data.get("items", [])
            
            for item in items:
                product_id = item.get("product_id")
                quantity = item.get("quantity")
                
                self.logger.log(
                    f"Order {order_id} includes {quantity} units of product {product_id}",
                    level="info",
                    app_name="products"
                )
            
            # TODO: Implement any business logic needed when orders are created
            
        except Exception as e:
            self.logger.log(
                f"Error handling ORDER_CREATED event: {e}",
                level="error",
                exception=e,
                app_name="products"
            )
    
    def start_consuming(self, topics: list = None):
        """Start consuming events from specified topics."""
        try:
            if topics is None:
                topics = [TOPICS['inventory'], TOPICS['orders']]
            
            self.consumer = create_consumer(
                group_id="products-service-consumer",
                topics=topics
            )
            
            self.running = True
            self.logger.log(
                f"Started consuming from topics: {topics}",
                level="info",
                app_name="products"
            )
            
            while self.running:
                msg = self.consumer.poll(timeout=1.0)
                
                if msg is None:
                    continue
                
                if msg.error():
                    if msg.error().code() == KafkaError._PARTITION_EOF:
                        continue
                    else:
                        self.logger.log(
                            f"Consumer error: {msg.error()}",
                            level="error",
                            app_name="products"
                        )
                        break
                
                # Process the message
                self._process_message(msg)
                
        except Exception as e:
            self.logger.log(
                f"Error in event consumer: {e}",
                level="error",
                exception=e,
                app_name="products"
            )
        finally:
            if self.consumer:
                self.consumer.close()
    
    def _process_message(self, msg):
        """Process a received Kafka message."""
        try:
            # Decode message
            message_value = msg.value().decode('utf-8')
            event_data = json.loads(message_value)
            
            event_type = event_data.get('event_type')
            if not event_type:
                self.logger.log(
                    "Received message without event_type",
                    level="warning",
                    app_name="products"
                )
                return
            
            self.logger.log(
                f"Received event: {event_type}",
                level="info",
                app_name="products"
            )
            
            # Find and execute handler
            handler = self.event_handlers.get(event_type)
            if handler:
                # Check if handler is async and run appropriately
                if asyncio.iscoroutinefunction(handler):
                    # Run async handler in new event loop if none exists
                    try:
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            # If loop is running, create task
                            asyncio.create_task(handler(event_data))
                        else:
                            # If no loop is running, run directly
                            loop.run_until_complete(handler(event_data))
                    except RuntimeError:
                        # No event loop exists, create and run
                        asyncio.run(handler(event_data))
                else:
                    # Synchronous handler
                    handler(event_data)
            else:
                self.logger.log(
                    f"No handler registered for event type: {event_type}",
                    level="debug",
                    app_name="products"
                )
                
        except Exception as e:
            self.logger.log(
                f"Error processing message: {e}",
                level="error",
                exception=e,
                app_name="products"
            )
    
    def stop_consuming(self):
        """Stop consuming events."""
        self.running = False
        self.logger.log(
            "Stopping event consumer",
            level="info",
            app_name="products"
        )


# Global event consumer instance
event_consumer = EventConsumer()
