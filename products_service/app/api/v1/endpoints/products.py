"""
Product API endpoints for Products Service.

This module provides REST API endpoints for product management following
the fehdan-inventory service patterns.
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.services.auth import require_write_access, require_read_access
from app.services.product import ProductService
from app.schemas import (
    ProductCreate,
    ProductResponse,
    ProductUpdate,
)

router = APIRouter()


def get_product_service(db: AsyncSession = Depends(get_db)) -> ProductService:
    """Dependency to get product service instance."""
    return ProductService(db)


@router.get(
    "/",
    response_model=List[ProductResponse],
    summary="List products",
    description="Get a list of all products with optional filtering",
)
async def list_products(
    category: Optional[str] = Query(None, description="Filter by category"),
    in_stock: Optional[bool] = Query(None, description="Filter by stock availability"),
    search: Optional[str] = Query(
        None, description="Search products by name or description"
    ),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of items to return"
    ),
    offset: int = Query(0, ge=0, description="Number of items to skip for pagination"),
    product_service: ProductService = Depends(get_product_service),
    current_user: Dict[str, Any] = Depends(require_read_access),
) -> List[ProductResponse]:
    """
    List products with optional filtering and pagination.

    - **category**: Optional filter by category
    - **in_stock**: Optional filter by stock availability
    - **search**: Optional search by name or description
    - **limit**: Maximum number of items to return (1-1000)
    - **offset**: Number of items to skip for pagination
    """
    return await product_service.list_products(
        category=category,
        in_stock=in_stock,
        search=search,
        limit=limit,
        offset=offset,
    )


@router.post(
    "/",
    response_model=ProductResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create product",
    description="Create a new product",
)
async def create_product(
    product_data: ProductCreate,
    product_service: ProductService = Depends(get_product_service),
    current_user: Dict[str, Any] = Depends(require_write_access),
) -> ProductResponse:
    """
    Create a new product.

    - **name**: Name of the product
    - **description**: Description of the product (optional)
    - **price**: Price of the product
    - **currency**: Currency code (default: USD)
    - **in_stock**: Whether the product is in stock (optional)
    - **stock_quantity**: Quantity in stock (optional)
    - **unity_of_measure**: Unit of measure (optional)
    - **categories**: List of category names (optional)
    - **tags**: List of tags (optional)
    - **images**: List of image URLs (optional)
    """
    return await product_service.create_product(current_user, product_data)


@router.get(
    "/{product_id}",
    response_model=ProductResponse,
    summary="Get product",
    description="Get a specific product by ID",
)
async def get_product(
    product_id: int,
    product_service: ProductService = Depends(get_product_service),
    current_user: Dict[str, Any] = Depends(require_read_access),
) -> ProductResponse:
    """
    Get a specific product by ID.

    - **product_id**: The ID of the product to retrieve
    """
    return await product_service.get_product_by_id(product_id)


@router.put(
    "/{product_id}",
    response_model=ProductResponse,
    summary="Update product",
    description="Update an existing product",
)
async def update_product(
    product_id: int,
    product_data: ProductUpdate,
    product_service: ProductService = Depends(get_product_service),
    current_user: Dict[str, Any] = Depends(require_write_access),
) -> ProductResponse:
    """
    Update an existing product.

    - **product_id**: The ID of the product to update
    - **name**: New name of the product (optional)
    - **description**: New description of the product (optional)
    - **price**: New price of the product (optional)
    - **currency**: New currency code (optional)
    - **in_stock**: New stock availability status (optional)
    - **stock_quantity**: New quantity in stock (optional)
    - **unity_of_measure**: New unit of measure (optional)
    - **categories**: New list of category names (optional)
    - **tags**: New list of tags (optional)
    - **images**: New list of image URLs (optional)
    """
    return await product_service.update_product(product_id, product_data, current_user)


@router.delete(
    "/{product_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete product",
    description="Delete a product",
)
async def delete_product(
    product_id: int,
    product_service: ProductService = Depends(get_product_service),
    current_user: Dict[str, Any] = Depends(require_write_access),
):
    """
    Delete a product.

    - **product_id**: The ID of the product to delete
    """
    await product_service.delete_product(product_id, current_user)


@router.get(
    "/{product_id}/availability",
    summary="Check product availability",
    description="Check if a product is available and get stock information",
)
async def check_product_availability(
    product_id: int,
    quantity: int = Query(1, ge=1, description="Quantity to check availability for"),
    product_service: ProductService = Depends(get_product_service),
    current_user: Dict[str, Any] = Depends(require_read_access),
) -> Dict[str, Any]:
    """
    Check product availability and stock information.

    - **product_id**: The ID of the product to check
    - **quantity**: Quantity to check availability for (default: 1)
    """
    return await product_service.check_availability(product_id, quantity)


@router.get(
    "/search/advanced",
    response_model=List[ProductResponse],
    summary="Advanced product search",
    description="Advanced search with multiple criteria",
)
async def advanced_search(
    name: Optional[str] = Query(None, description="Search by product name"),
    description: Optional[str] = Query(None, description="Search by description"),
    min_price: Optional[float] = Query(None, ge=0, description="Minimum price"),
    max_price: Optional[float] = Query(None, ge=0, description="Maximum price"),
    categories: Optional[List[str]] = Query(None, description="Filter by categories"),
    tags: Optional[List[str]] = Query(None, description="Filter by tags"),
    in_stock: Optional[bool] = Query(None, description="Filter by stock availability"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of results"),
    offset: int = Query(0, ge=0, description="Number of items to skip"),
    product_service: ProductService = Depends(get_product_service),
    current_user: Dict[str, Any] = Depends(require_read_access),
) -> List[ProductResponse]:
    """
    Advanced product search with multiple criteria.

    - **name**: Search by product name
    - **description**: Search by description
    - **min_price**: Minimum price filter
    - **max_price**: Maximum price filter
    - **categories**: Filter by categories
    - **tags**: Filter by tags
    - **in_stock**: Filter by stock availability
    - **limit**: Maximum number of results (1-1000)
    - **offset**: Number of items to skip for pagination
    """
    return await product_service.advanced_search(
        name=name,
        description=description,
        min_price=min_price,
        max_price=max_price,
        categories=categories,
        tags=tags,
        in_stock=in_stock,
        limit=limit,
        offset=offset,
    )
