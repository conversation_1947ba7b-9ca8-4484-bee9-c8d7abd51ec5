from fastapi import HTTPException
from products_service.model import Category
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import delete
from shared.core.utils.logging import LoggingService
from typing import List, Optional


class CategoryRepository:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.logger = LoggingService()

    async def create_category(self, name: str, description: Optional[str] = None, is_active: Optional[bool] = True) -> Category:
        """Create a new category."""
        try:
            existing = await self.get_category_by_name(name)
            if existing:
                return existing

            db_category = Category(name=name, description=description, is_active=is_active)
            self.db.add(db_category)
            await self.db.commit()
            await self.db.refresh(db_category)
            return db_category
        except HTTPException:
            await self.db.rollback()
            raise
        except Exception as e:
            await self.db.rollback()
            self.logger.log(
                message=f"Error creating category '{name}'",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(status_code=500, detail="Error creating category")

    async def get_category_by_id(self, category_id: int) -> Optional[Category]:
        """Retrieve a category by ID."""
        try:
            result = await self.db.execute(select(Category).filter_by(id=category_id))
            return result.scalar_one_or_none()
        except Exception as e:
            self.logger.log(
                message="Error retrieving category by ID",
                level="error",
                exception=e,
                app_name="products",
            )
            return None

    async def get_category_by_name(self, name: str) -> Optional[Category]:
        """Retrieve a category by name."""
        try:
            result = await self.db.execute(select(Category).filter_by(name=name))
            return result.scalar_one_or_none()
        except Exception as e:
            self.logger.log(
                message="Error retrieving category by name",
                level="error",
                exception=e,
                app_name="products",
            )
            return None

    async def list_categories(self, active_only: bool = True) -> List[Category]:
        """List all categories."""
        try:
            query = select(Category)
            if active_only:
                query = query.where(Category.is_active == True)
            query = query.order_by(Category.name)

            result = await self.db.execute(query)
            return result.scalars().all()
        except Exception as e:
            self.logger.log(
                message="Error listing categories",
                level="error",
                exception=e,
                app_name="products",
            )
            return []

    async def update_category(self, category_id: int, name: Optional[str] = None,
                            description: Optional[str] = None, is_active: Optional[bool] = None) -> Optional[Category]:
        """Update category details."""
        try:
            result = await self.db.execute(select(Category).where(Category.id == category_id))
            category = result.scalar_one_or_none()

            if not category:
                raise HTTPException(status_code=404, detail="Category not found")

            if name and name != category.name:
                existing = await self.get_category_by_name(name)
                if existing:
                    raise HTTPException(status_code=400, detail=f"Category '{name}' already exists")
                category.name = name

            if description is not None:
                category.description = description
            if is_active is not None:
                category.is_active = is_active

            await self.db.commit()
            await self.db.refresh(category)
            return category
        except HTTPException:
            await self.db.rollback()
            raise
        except Exception as e:
            await self.db.rollback()
            self.logger.log(
                message="Error updating category",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(status_code=500, detail="Error updating category")

    async def delete_category(self, category_id: int) -> None:
        """Delete a category by ID."""
        try:
            result = await self.db.execute(select(Category).filter_by(id=category_id))
            category = result.scalar_one_or_none()
            if not category:
                raise HTTPException(status_code=404, detail="Category not found")

            await self.db.execute(delete(Category).where(Category.id == category_id))
            await self.db.commit()
        except HTTPException:
            await self.db.rollback()
            raise
        except Exception as e:
            await self.db.rollback()
            self.logger.log(
                message="Error deleting category",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(status_code=500, detail="Error deleting category")

    async def get_or_create_categories(self, category_names: List[str]) -> List[Category]:
        """Get existing categories or create new ones for the given names."""
        try:
            categories = []
            for name in category_names:
                if not name or not name.strip():
                    continue

                name = name.strip()
                category = await self.get_category_by_name(name)
                if not category:
                    category = await self.create_category(name)
                categories.append(category)
            return categories
        except Exception as e:
            self.logger.log(
                message=f"Error getting or creating categories: {category_names}",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(status_code=500, detail="Error processing categories")
